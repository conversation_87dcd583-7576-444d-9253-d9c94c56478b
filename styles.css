/* ===== RESET E CONFIGURAÇÕES GLOBAIS ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* <PERSON>s principais */
    --primary-purple: #8000ff;
    --primary-black: #000000;
    --dark-gray: #0a0a0a;
    --medium-gray: #1a1a1a;
    --light-gray: #2a2a2a;
    --lighter-gray: #404040;
    --white: #ffffff;
    --off-white: #f8f9fa;

    /* Cores de acento tecnológicas */
    --purple-light: #a855f7;
    --purple-dark: #6b21a8;
    --neon-green: #00ff88;
    --neon-blue: #00d4ff;
    --neon-pink: #ff0080;
    --cyber-yellow: #ffff00;

    /* Gradientes futuristas */
    --gradient-primary: linear-gradient(135deg, #8000ff 0%, #a855f7 50%, #00d4ff 100%);
    --gradient-dark: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #2a2a2a 100%);
    --gradient-neon: linear-gradient(45deg, #ff0080, #8000ff, #00d4ff, #00ff88);
    --gradient-cyber: linear-gradient(90deg, #000000, #8000ff, #000000);
    --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);

    /* Efeitos holográficos */
    --hologram-1: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.5) 50%, transparent 70%);
    --hologram-2: linear-gradient(90deg, #ff0080, #8000ff, #00d4ff, #00ff88, #ffff00);
    --hologram-3: conic-gradient(from 0deg, #ff0080, #8000ff, #00d4ff, #00ff88, #ffff00, #ff0080);
    
    /* Tipografia */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;
    
    /* Espaçamentos */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    --spacing-4xl: 6rem;
    
    /* Sombras */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-purple: 0 10px 30px rgba(128, 0, 255, 0.3);
    
    /* Transições */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Border radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--medium-gray);
    background-color: var(--white);
    overflow-x: hidden;
}

/* ===== UTILITÁRIOS ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-md);
    }
}

/* Animações de entrada */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Classes de animação */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out forwards;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out forwards;
}

/* ===== HEADER E NAVEGAÇÃO ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    border-bottom: 2px solid transparent;
    border-image: var(--gradient-neon) 1;
    transition: var(--transition-normal);
    box-shadow: 0 4px 32px rgba(128, 0, 255, 0.3);
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--hologram-1);
    opacity: 0;
    transition: var(--transition-normal);
    pointer-events: none;
}

.header:hover::before {
    opacity: 0.1;
}

.header.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-md);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.logo h2 {
    font-size: var(--font-size-2xl);
    font-weight: 800;
    background: var(--hologram-2);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: hologramShift 3s ease-in-out infinite;
    position: relative;
    text-shadow: 0 0 20px rgba(128, 0, 255, 0.5);
}

.logo h2::before {
    content: 'Flecty';
    position: absolute;
    top: 0;
    left: 0;
    background: var(--gradient-neon);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: neonGlow 2s ease-in-out infinite alternate;
    z-index: -1;
}

@keyframes hologramShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes neonGlow {
    0% { opacity: 0.5; filter: blur(1px); }
    100% { opacity: 1; filter: blur(0px); }
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-xl);
    align-items: center;
}

.nav-menu a {
    text-decoration: none;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    transition: var(--transition-fast);
    position: relative;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    overflow: hidden;
}

.nav-menu a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: var(--transition-normal);
    z-index: -1;
}

.nav-menu a:hover {
    color: var(--white);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.nav-menu a:hover::before {
    left: 0;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--neon-blue);
    box-shadow: 0 0 10px var(--neon-blue);
    transition: var(--transition-fast);
}

.nav-menu a:hover::after {
    width: 100%;
}

.cta-nav {
    background: var(--gradient-primary) !important;
    color: var(--white) !important;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-weight: 600;
    transition: var(--transition-fast);
}

.cta-nav:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-purple);
}

.cta-nav::after {
    display: none;
}

/* Menu hamburger para mobile */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--primary-purple);
    transition: var(--transition-fast);
    border-radius: 2px;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: var(--white);
        flex-direction: column;
        justify-content: flex-start;
        padding-top: var(--spacing-2xl);
        transition: var(--transition-normal);
        box-shadow: var(--shadow-lg);
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .nav-container {
        padding: var(--spacing-md);
    }
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding-top: 80px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-dark);
    z-index: -3;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(128, 0, 255, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 212, 255, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 0, 128, 0.2) 0%, transparent 50%);
    animation: techPulse 4s ease-in-out infinite;
    z-index: -2;
}

@keyframes techPulse {
    0%, 100% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.1); }
}

.tech-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    opacity: 0.6;
}

.gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(128, 0, 255, 0.1) 50%, rgba(0, 0, 0, 0.8) 100%),
        repeating-linear-gradient(
            90deg,
            transparent,
            transparent 98px,
            rgba(128, 0, 255, 0.03) 100px
        );
    z-index: -1;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: -1;
}

.floating-element {
    position: absolute;
    background: var(--hologram-3);
    border-radius: 50%;
    opacity: 0.3;
    animation: float 6s ease-in-out infinite, techRotate 8s linear infinite;
    filter: blur(1px);
    box-shadow:
        0 0 20px rgba(128, 0, 255, 0.5),
        inset 0 0 20px rgba(255, 255, 255, 0.1);
}

.floating-element::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2px;
    height: 2px;
    background: var(--neon-blue);
    border-radius: 50%;
    box-shadow: 0 0 10px var(--neon-blue);
    transform: translate(-50%, -50%);
    animation: corePulse 2s ease-in-out infinite;
}

@keyframes techRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes corePulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
    50% { transform: translate(-50%, -50%) scale(3); opacity: 0.5; }
}

.floating-element:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
    width: 80px;
    height: 80px;
    background: conic-gradient(from 0deg, #ff0080, #8000ff, #00d4ff);
}

.floating-element:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
    width: 120px;
    height: 120px;
    background: conic-gradient(from 120deg, #00ff88, #ffff00, #ff0080);
}

.floating-element:nth-child(3) {
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
    width: 60px;
    height: 60px;
    background: conic-gradient(from 240deg, #00d4ff, #8000ff, #00ff88);
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4xl);
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    position: relative;
    z-index: 1;
}

.hero-text {
    color: var(--white);
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: var(--spacing-lg);
    opacity: 0;
    animation: fadeInLeft 1s ease-out 0.3s forwards;
    color: var(--white);
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
    position: relative;
}

.hero-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--hologram-1);
    opacity: 0;
    animation: titleScan 3s ease-in-out infinite;
    pointer-events: none;
}

@keyframes titleScan {
    0%, 100% { opacity: 0; transform: translateX(-100%); }
    50% { opacity: 0.3; transform: translateX(100%); }
}

.hero-title .highlight {
    background: var(--hologram-2);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: hologramShift 2s ease-in-out infinite;
    position: relative;
    display: inline-block;
}

.hero-title .highlight::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: textGlitch 4s ease-in-out infinite;
    pointer-events: none;
}

@keyframes textGlitch {
    0%, 90%, 100% { opacity: 0; transform: translateX(0); }
    95% { opacity: 1; transform: translateX(2px); }
}

.hero-subtitle {
    font-size: var(--font-size-lg);
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: var(--spacing-2xl);
    max-width: 500px;
    opacity: 0;
    animation: fadeInLeft 1s ease-out 0.6s forwards;
}

.hero-cta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
    opacity: 0;
    animation: fadeInLeft 1s ease-out 0.9s forwards;
    align-items: center;
    justify-content: flex-start;
    margin-top: var(--spacing-xl);
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    border: 2px solid transparent;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-full);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    display: inline-block;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow:
        0 0 20px rgba(128, 0, 255, 0.5),
        inset 0 0 20px rgba(255, 255, 255, 0.1);
    z-index: 1;
    min-width: 200px;
    white-space: nowrap;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    font-family: var(--font-family);
    letter-spacing: 0.5px;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-normal);
    z-index: -1;
}

.btn-primary::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    transition: var(--transition-normal);
    transform: translate(-50%, -50%);
    border-radius: 50%;
    z-index: -1;
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        0 10px 40px rgba(128, 0, 255, 0.8),
        0 0 30px rgba(0, 212, 255, 0.5),
        inset 0 0 30px rgba(255, 255, 255, 0.2);
    border-color: var(--neon-blue);
    color: var(--white);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover::after {
    width: 80px;
    height: 80px;
}

.btn-secondary {
    background: rgba(0, 0, 0, 0.3);
    color: var(--white);
    border: 2px solid rgba(128, 0, 255, 0.6);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-full);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    display: inline-block;
    text-align: center;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    z-index: 1;
    min-width: 180px;
    white-space: nowrap;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5), 0 0 5px rgba(255, 255, 255, 0.3);
    font-family: var(--font-family);
    letter-spacing: 0.5px;
}

.btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition-normal);
    z-index: -1;
    border-radius: var(--radius-full);
}

.btn-secondary::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(0, 212, 255, 0.2) 0%, transparent 70%);
    transition: var(--transition-normal);
    transform: translate(-50%, -50%);
    border-radius: 50%;
    z-index: -1;
}

.btn-secondary:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow:
        0 5px 25px rgba(0, 212, 255, 0.5),
        inset 0 0 20px rgba(255, 255, 255, 0.1);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.9);
    border-color: var(--neon-blue);
    color: var(--white);
}

.btn-secondary:hover::before {
    opacity: 0.3;
}

.btn-secondary:hover::after {
    width: 60px;
    height: 60px;
}

.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    animation: fadeInRight 1s ease-out 0.6s forwards;
}

.hero-card {
    background: var(--gradient-glass);
    backdrop-filter: blur(30px);
    border: 2px solid transparent;
    border-image: var(--gradient-neon) 1;
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(128, 0, 255, 0.3),
        inset 0 0 30px rgba(255, 255, 255, 0.1);
    transform: perspective(1000px) rotateY(-10deg);
    transition: var(--transition-slow);
    position: relative;
    overflow: hidden;
}

.hero-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--hologram-1);
    opacity: 0;
    transition: var(--transition-slow);
    pointer-events: none;
}

.hero-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(128, 0, 255, 0.1), transparent);
    animation: cardRotate 8s linear infinite;
    z-index: -1;
}

@keyframes cardRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hero-card:hover {
    transform: perspective(1000px) rotateY(0deg) scale(1.05);
    box-shadow:
        0 30px 60px rgba(0, 0, 0, 0.4),
        0 0 50px rgba(0, 212, 255, 0.5),
        inset 0 0 50px rgba(255, 255, 255, 0.2);
}

.hero-card:hover::before {
    opacity: 0.3;
}

.card-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.metric {
    text-align: center;
}

.metric-number {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: 800;
    background: var(--hologram-2);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: hologramShift 3s ease-in-out infinite;
    position: relative;
    text-shadow: 0 0 20px rgba(128, 0, 255, 0.5);
}

.metric-number::before {
    content: attr(data-number);
    position: absolute;
    top: 0;
    left: 0;
    background: var(--neon-blue);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    opacity: 0;
    animation: numberGlitch 4s ease-in-out infinite;
}

@keyframes numberGlitch {
    0%, 95%, 100% { opacity: 0; transform: translateX(0); }
    97% { opacity: 0.8; transform: translateX(2px); }
}

.metric-label {
    display: block;
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.8);
    margin-top: var(--spacing-xs);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    position: relative;
}

.metric-label::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 1px;
    background: var(--gradient-neon);
    opacity: 0.5;
    animation: labelPulse 2s ease-in-out infinite;
}

@keyframes labelPulse {
    0%, 100% { opacity: 0.3; transform: scaleX(0.8); }
    50% { opacity: 0.8; transform: scaleX(1); }
}

.scroll-indicator {
    position: absolute;
    bottom: var(--spacing-xl);
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    animation: fadeInUp 1s ease-out 1.2s forwards;
}

.scroll-arrow {
    width: 30px;
    height: 30px;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-top: none;
    border-left: none;
    transform: rotate(45deg);
    animation: pulse 2s infinite;
}

@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
        text-align: center;
        padding: 0 var(--spacing-md);
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-base);
        max-width: none;
    }

    .hero-cta {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }

    .btn-primary,
    .btn-secondary {
        width: 100%;
        max-width: 280px;
        min-width: auto;
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-sm);
    }
}

/* ===== SEÇÕES GERAIS ===== */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-4xl);
}

.section-title {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    color: var(--primary-black);
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--light-gray);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== SOBRE SECTION ===== */
.sobre {
    padding: var(--spacing-4xl) 0;
    background: var(--off-white);
}

.sobre-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-2xl);
    margin-top: var(--spacing-3xl);
}

.sobre-card {
    background: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    border: 1px solid rgba(128, 0, 255, 0.1);
}

.sobre-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-purple);
}

.card-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-lg);
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
}

.card-icon svg {
    width: 30px;
    height: 30px;
}

.sobre-card h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-black);
    margin-bottom: var(--spacing-md);
}

.sobre-card p {
    color: var(--light-gray);
    line-height: 1.6;
}

/* ===== SERVIÇOS SECTION ===== */
.servicos {
    padding: var(--spacing-4xl) 0;
    background: var(--white);
}

.service-category {
    margin-bottom: var(--spacing-4xl);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    background: linear-gradient(135deg, rgba(128, 0, 255, 0.02) 0%, rgba(0, 0, 0, 0.02) 100%);
    border: 1px solid rgba(128, 0, 255, 0.1);
    transition: var(--transition-normal);
}

.service-category:hover {
    border-color: var(--primary-purple);
    box-shadow: var(--shadow-lg);
}

.service-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.service-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    flex-shrink: 0;
}

.service-icon.marketing {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.service-icon.design {
    background: var(--gradient-primary);
}

.service-icon.web {
    background: linear-gradient(135deg, #00d2d3, #54a0ff);
}

.service-icon svg {
    width: 40px;
    height: 40px;
}

.service-title h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-black);
    margin-bottom: var(--spacing-xs);
}

.service-title p {
    color: var(--light-gray);
    font-size: var(--font-size-base);
}

.service-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.service-item {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(128, 0, 255, 0.1);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.service-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.service-item:hover::before {
    transform: scaleX(1);
}

.service-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-purple);
}

.service-item.featured {
    border-color: var(--primary-purple);
    background: linear-gradient(135deg, rgba(128, 0, 255, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
}

.service-item.featured::before {
    transform: scaleX(1);
}

.service-item h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-black);
    margin-bottom: var(--spacing-md);
}

.service-item p {
    color: var(--light-gray);
    line-height: 1.6;
}

@media (max-width: 768px) {
    .section-title {
        font-size: var(--font-size-3xl);
    }

    .service-header {
        flex-direction: column;
        text-align: center;
    }

    .service-items {
        grid-template-columns: 1fr;
    }

    .sobre-grid {
        grid-template-columns: 1fr;
    }
}

/* ===== DEPOIMENTOS SECTION ===== */
.depoimentos {
    padding: var(--spacing-4xl) 0;
    background: var(--gradient-dark);
    color: var(--white);
}

.depoimentos .section-title {
    color: var(--white);
}

.depoimentos .section-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

.depoimentos-carousel {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    overflow: hidden;
}

.depoimento-card {
    display: none;
    opacity: 0;
    transform: translateX(50px);
    transition: var(--transition-slow);
}

.depoimento-card.active {
    display: block;
    opacity: 1;
    transform: translateX(0);
}

.depoimento-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    text-align: center;
}

.stars {
    font-size: var(--font-size-xl);
    color: #ffd700;
    margin-bottom: var(--spacing-lg);
}

.depoimento-content p {
    font-size: var(--font-size-lg);
    line-height: 1.6;
    margin-bottom: var(--spacing-xl);
    font-style: italic;
}

.depoimento-author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
}

.author-info h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.author-info span {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.7);
}

.carousel-dots {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-2xl);
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: var(--transition-fast);
}

.dot.active {
    background: var(--primary-purple);
    transform: scale(1.2);
}

/* ===== CONTATO SECTION ===== */
.contato {
    padding: var(--spacing-4xl) 0;
    background: var(--off-white);
    position: relative;
}

.contato-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4xl);
    align-items: start;
}

.contato-info h2 {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    color: var(--primary-black);
    margin-bottom: var(--spacing-lg);
}

.contato-info p {
    font-size: var(--font-size-lg);
    color: var(--light-gray);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.6;
}

.contact-benefits {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.benefit {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.benefit-icon {
    width: 24px;
    height: 24px;
    background: var(--gradient-primary);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    font-weight: 600;
    flex-shrink: 0;
}

.benefit span:last-child {
    color: var(--medium-gray);
    font-weight: 500;
}

.contact-form {
    background: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(128, 0, 255, 0.1);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid rgba(128, 0, 255, 0.1);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    transition: var(--transition-fast);
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-purple);
    box-shadow: 0 0 0 3px rgba(128, 0, 255, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.btn-submit {
    width: 100%;
    background: var(--gradient-primary);
    color: var(--white);
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-purple);
}

.form-feedback {
    position: fixed;
    top: 100px;
    right: var(--spacing-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-weight: 500;
    z-index: 1000;
    transform: translateX(400px);
    transition: var(--transition-normal);
}

.form-feedback.show {
    transform: translateX(0);
}

.form-feedback.success {
    background: #10b981;
    color: var(--white);
}

.form-feedback.error {
    background: #ef4444;
    color: var(--white);
}

@media (max-width: 768px) {
    .contato-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }

    .contato-info h2 {
        font-size: var(--font-size-3xl);
    }

    .form-feedback {
        right: var(--spacing-md);
        left: var(--spacing-md);
        transform: translateY(-100px);
    }

    .form-feedback.show {
        transform: translateY(0);
    }
}

/* ===== WHATSAPP BUTTON ===== */
.whatsapp-btn {
    position: fixed;
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
    width: 60px;
    height: 60px;
    background: #25d366;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    transition: var(--transition-normal);
    animation: pulse 2s infinite;
}

.whatsapp-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 10px 30px rgba(37, 211, 102, 0.4);
}

.whatsapp-btn svg {
    width: 30px;
    height: 30px;
}

@media (max-width: 768px) {
    .whatsapp-btn {
        bottom: var(--spacing-lg);
        right: var(--spacing-lg);
        width: 50px;
        height: 50px;
    }

    .whatsapp-btn svg {
        width: 25px;
        height: 25px;
    }
}

/* ===== FOOTER ===== */
.footer {
    background: var(--primary-black);
    color: var(--white);
    padding: var(--spacing-4xl) 0 var(--spacing-xl);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.footer-brand h3 {
    font-size: var(--font-size-2xl);
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-md);
}

.footer-brand p {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6;
}

.footer-column h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    color: var(--white);
}

.footer-column ul {
    list-style: none;
}

.footer-column ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-column ul li a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-column ul li a:hover {
    color: var(--primary-purple);
}

.footer-social h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    color: var(--white);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: var(--transition-fast);
}

.social-links a:hover {
    background: var(--primary-purple);
    color: var(--white);
    transform: translateY(-2px);
}

.social-links a svg {
    width: 20px;
    height: 20px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: var(--spacing-xl);
    text-align: center;
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.5);
    font-size: var(--font-size-sm);
}

@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }

    .social-links {
        justify-content: center;
    }
}

/* ===== ANIMAÇÕES DE SCROLL ===== */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: var(--transition-slow);
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* ===== RESPONSIVIDADE ADICIONAL ===== */
@media (max-width: 480px) {
    :root {
        --font-size-5xl: 2.25rem;
        --font-size-4xl: 1.875rem;
        --font-size-3xl: 1.5rem;
    }

    .hero {
        min-height: 90vh;
    }

    .hero-cta {
        gap: var(--spacing-md);
    }

    .btn-primary,
    .btn-secondary {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--font-size-sm);
    }

    .section-header {
        margin-bottom: var(--spacing-2xl);
    }

    .sobre,
    .servicos,
    .depoimentos,
    .contato {
        padding: var(--spacing-2xl) 0;
    }
}

/* ===== MELHORIAS DE PERFORMANCE ===== */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

img {
    max-width: 100%;
    height: auto;
}

/* ===== ESTADOS DE LOADING ===== */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--primary-purple);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
