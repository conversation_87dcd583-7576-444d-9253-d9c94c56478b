/* ===== RESET E CONFIGURAÇÕES GLOBAIS ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Cores principais */
    --primary-purple: #8000ff;
    --primary-black: #000000;
    --dark-gray: #1a1a1a;
    --medium-gray: #333333;
    --light-gray: #666666;
    --lighter-gray: #999999;
    --white: #ffffff;
    --off-white: #f8f9fa;
    
    /* Cores de acento */
    --purple-light: #9933ff;
    --purple-dark: #6600cc;
    --neon-green: #00ff88;
    --gradient-primary: linear-gradient(135deg, var(--primary-purple) 0%, var(--purple-light) 100%);
    --gradient-dark: linear-gradient(135deg, var(--primary-black) 0%, var(--dark-gray) 100%);
    
    /* Tipografia */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;
    
    /* Espaçamentos */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    --spacing-4xl: 6rem;
    
    /* Sombras */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-purple: 0 10px 30px rgba(128, 0, 255, 0.3);
    
    /* Transições */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Border radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--medium-gray);
    background-color: var(--white);
    overflow-x: hidden;
}

/* ===== UTILITÁRIOS ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-md);
    }
}

/* Animações de entrada */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Classes de animação */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out forwards;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out forwards;
}

/* ===== HEADER E NAVEGAÇÃO ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(128, 0, 255, 0.1);
    transition: var(--transition-normal);
}

.header.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-md);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.logo h2 {
    font-size: var(--font-size-2xl);
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-xl);
    align-items: center;
}

.nav-menu a {
    text-decoration: none;
    color: var(--medium-gray);
    font-weight: 500;
    transition: var(--transition-fast);
    position: relative;
}

.nav-menu a:hover {
    color: var(--primary-purple);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: var(--transition-fast);
}

.nav-menu a:hover::after {
    width: 100%;
}

.cta-nav {
    background: var(--gradient-primary) !important;
    color: var(--white) !important;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-weight: 600;
    transition: var(--transition-fast);
}

.cta-nav:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-purple);
}

.cta-nav::after {
    display: none;
}

/* Menu hamburger para mobile */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--primary-purple);
    transition: var(--transition-fast);
    border-radius: 2px;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: var(--white);
        flex-direction: column;
        justify-content: flex-start;
        padding-top: var(--spacing-2xl);
        transition: var(--transition-normal);
        box-shadow: var(--shadow-lg);
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .nav-container {
        padding: var(--spacing-md);
    }
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    padding-top: 80px;
}

/* Barra de urgência */
.urgency-bar {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: linear-gradient(90deg, #ff4757, #ff3742);
    color: white;
    padding: var(--spacing-sm) 0;
    z-index: 999;
    animation: urgencyPulse 2s infinite;
}

@keyframes urgencyPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.9; }
}

.urgency-content {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-md);
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.urgency-timer {
    background: rgba(0, 0, 0, 0.3);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-family: 'Courier New', monospace;
}

/* Partículas neurais */
.neural-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

#particleCanvas {
    width: 100%;
    height: 100%;
}

/* Badge de autoridade */
.authority-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: var(--primary-black);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-weight: 600;
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    animation: fadeInLeft 1s ease-out 0.2s forwards;
    opacity: 0;
}

/* Título hero reformulado */
.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: var(--spacing-lg);
    opacity: 0;
    animation: fadeInLeft 1s ease-out 0.3s forwards;
    text-align: center;
}

.title-line {
    display: block;
    color: var(--white);
}

.title-highlight {
    display: block;
    font-size: var(--font-size-6xl);
    background: linear-gradient(135deg, #ff6b6b, #ffd700);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(255, 107, 107, 0.5);
}

.title-subtitle {
    display: block;
    font-size: var(--font-size-lg);
    color: rgba(255, 255, 255, 0.8);
    font-weight: 400;
    margin-top: var(--spacing-sm);
}

.percentage {
    color: #00ff88;
    text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
}

/* Prova social mini */
.social-proof-mini {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    opacity: 0;
    animation: fadeInLeft 1s ease-out 0.6s forwards;
}

.proof-avatars {
    display: flex;
    align-items: center;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid var(--white);
    margin-left: -10px;
    background: linear-gradient(135deg, var(--primary-purple), var(--purple-light));
}

.avatar:first-child {
    margin-left: 0;
}

.avatar-more {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    border: 3px solid var(--white);
    margin-left: -10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xs);
    font-weight: 600;
    color: var(--white);
}

.proof-text {
    color: rgba(255, 255, 255, 0.9);
    font-size: var(--font-size-sm);
}

/* Lista de benefícios */
.benefits-list {
    margin-bottom: var(--spacing-2xl);
    opacity: 0;
    animation: fadeInLeft 1s ease-out 0.8s forwards;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    color: var(--white);
}

.benefit-icon {
    width: 24px;
    height: 24px;
    background: var(--neon-green);
    color: var(--primary-black);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 800;
    font-size: var(--font-size-sm);
    flex-shrink: 0;
}

/* CTA melhorado */
.hero-cta {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    opacity: 0;
    animation: fadeInLeft 1s ease-out 1s forwards;
}

.btn-primary {
    background: linear-gradient(135deg, #ff6b6b, #ff4757);
    color: var(--white);
    border: none;
    padding: var(--spacing-lg) var(--spacing-2xl);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-lg);
    font-weight: 700;
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: var(--transition-slow);
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 40px rgba(255, 107, 107, 0.6);
}

.pulse-effect {
    animation: pulse 2s infinite;
}

.btn-text {
    font-size: var(--font-size-lg);
    font-weight: 800;
}

.btn-subtext {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    font-weight: 400;
}

.cta-security {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--font-size-sm);
}

/* Showcase de resultados */
.results-showcase {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-xl);
    opacity: 0;
    animation: fadeInRight 1s ease-out 0.6s forwards;
}

.showcase-header h3 {
    color: var(--white);
    font-size: var(--font-size-xl);
    font-weight: 700;
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.results-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.result-card {
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.result-metric {
    font-size: var(--font-size-2xl);
    font-weight: 800;
    color: var(--neon-green);
    margin-bottom: var(--spacing-xs);
}

.result-label {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: var(--spacing-xs);
}

.result-client {
    font-size: var(--font-size-xs);
    color: rgba(255, 255, 255, 0.6);
}

.showcase-footer {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--font-size-sm);
}

.live-indicator {
    animation: pulse 1.5s infinite;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-dark);
    z-index: -2;
}

.gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(128, 0, 255, 0.1) 0%,
        rgba(0, 0, 0, 0.8) 50%,
        rgba(128, 0, 255, 0.2) 100%);
    z-index: -1;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: -1;
}

.floating-element {
    position: absolute;
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, var(--primary-purple), var(--purple-light));
    border-radius: 50%;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
    width: 80px;
    height: 80px;
}

.floating-element:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
    width: 120px;
    height: 120px;
}

.floating-element:nth-child(3) {
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
    width: 60px;
    height: 60px;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4xl);
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    position: relative;
    z-index: 1;
}

.hero-text {
    color: var(--white);
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: var(--spacing-lg);
    opacity: 0;
    animation: fadeInLeft 1s ease-out 0.3s forwards;
}

.hero-title .highlight {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: var(--font-size-lg);
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: var(--spacing-2xl);
    max-width: 500px;
    opacity: 0;
    animation: fadeInLeft 1s ease-out 0.6s forwards;
}

.hero-cta {
    display: flex;
    gap: var(--spacing-lg);
    opacity: 0;
    animation: fadeInLeft 1s ease-out 0.9s forwards;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-full);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-purple);
}

.btn-secondary {
    background: transparent;
    color: var(--white);
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-full);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--primary-purple);
    transform: translateY(-2px);
}

.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    animation: fadeInRight 1s ease-out 0.6s forwards;
}

.hero-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-xl);
    transform: perspective(1000px) rotateY(-10deg);
    transition: var(--transition-slow);
}

.hero-card:hover {
    transform: perspective(1000px) rotateY(0deg);
}

.card-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.metric {
    text-align: center;
}

.metric-number {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: var(--white);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.metric-label {
    display: block;
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.7);
    margin-top: var(--spacing-xs);
}

.scroll-indicator {
    position: absolute;
    bottom: var(--spacing-xl);
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    animation: fadeInUp 1s ease-out 1.2s forwards;
}

.scroll-arrow {
    width: 30px;
    height: 30px;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-top: none;
    border-left: none;
    transform: rotate(45deg);
    animation: pulse 2s infinite;
}

@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
        text-align: center;
        padding: 0 var(--spacing-md);
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-base);
        max-width: none;
    }

    .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .btn-primary,
    .btn-secondary {
        width: 100%;
        max-width: 300px;
    }
}

/* ===== SEÇÕES GERAIS ===== */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-4xl);
}

.section-title {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    color: var(--primary-black);
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--light-gray);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== PROBLEMA/SOLUÇÃO SECTION ===== */
.problema-solucao {
    padding: var(--spacing-4xl) 0;
    background: var(--off-white);
}

/* Seção do Problema */
.problema-section {
    margin-bottom: var(--spacing-4xl);
}

.pain-title {
    color: var(--primary-black);
}

.highlight-red {
    color: #ff4757;
    text-shadow: 0 0 20px rgba(255, 71, 87, 0.3);
}

.problemas-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-2xl);
    margin-top: var(--spacing-3xl);
}

.problema-card {
    background: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    border: 1px solid rgba(255, 71, 87, 0.1);
    position: relative;
    overflow: hidden;
}

.problema-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff4757, #ff6b6b);
}

.problema-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
    border-color: #ff4757;
}

.problema-icon {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-lg);
}

.problema-card h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-black);
    margin-bottom: var(--spacing-md);
}

.problema-card p {
    color: var(--light-gray);
    line-height: 1.6;
}

/* Transição emocional */
.transition-section {
    text-align: center;
    margin: var(--spacing-4xl) 0;
    padding: var(--spacing-2xl) 0;
}

.transition-content h2 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-black);
    margin-bottom: var(--spacing-lg);
}

.transition-arrow {
    font-size: var(--font-size-4xl);
    color: var(--primary-purple);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Seção da Solução */
.solution-title {
    color: var(--primary-black);
}

.highlight-green {
    color: #00ff88;
    text-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
}

.solucoes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-2xl);
    margin-top: var(--spacing-3xl);
}

.solucao-card {
    background: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    border: 1px solid rgba(0, 255, 136, 0.1);
    position: relative;
    overflow: hidden;
}

.solucao-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #00ff88, #00d2d3);
}

.solucao-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
    border-color: #00ff88;
}

.card-number {
    position: absolute;
    top: -10px;
    right: var(--spacing-lg);
    background: var(--gradient-primary);
    color: var(--white);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 800;
    font-size: var(--font-size-lg);
}

.card-icon.success {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-lg);
    background: linear-gradient(135deg, #00ff88, #00d2d3);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
}

.card-icon.success svg {
    width: 30px;
    height: 30px;
}

.solucao-card h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-black);
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.solucao-card p {
    color: var(--light-gray);
    line-height: 1.6;
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.card-result {
    background: linear-gradient(135deg, rgba(0, 255, 136, 0.1), rgba(0, 210, 211, 0.1));
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    text-align: center;
    font-weight: 600;
    color: #00a86b;
    border: 1px solid rgba(0, 255, 136, 0.3);
}

/* Prova social forte */
.proof-section {
    margin-top: var(--spacing-4xl);
    padding: var(--spacing-2xl);
    background: var(--gradient-dark);
    border-radius: var(--radius-2xl);
    color: var(--white);
}

.proof-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-xl);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: block;
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-base);
}

.sobre-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-2xl);
    margin-top: var(--spacing-3xl);
}

.sobre-card {
    background: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    border: 1px solid rgba(128, 0, 255, 0.1);
}

.sobre-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-purple);
}

.card-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-lg);
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
}

.card-icon svg {
    width: 30px;
    height: 30px;
}

.sobre-card h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-black);
    margin-bottom: var(--spacing-md);
}

.sobre-card p {
    color: var(--light-gray);
    line-height: 1.6;
}

/* ===== SERVIÇOS SECTION ===== */
.servicos {
    padding: var(--spacing-4xl) 0;
    background: var(--white);
}

.guarantee-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: linear-gradient(135deg, #00ff88, #00d2d3);
    color: var(--primary-black);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-weight: 700;
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    animation: pulse 2s infinite;
}

/* Pacotes de Transformação */
.transformation-packages {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-2xl);
    margin-top: var(--spacing-3xl);
}

.package-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.package-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.package-card.starter {
    border-color: var(--primary-purple);
}

.package-card.professional {
    border-color: #ffd700;
    transform: scale(1.05);
}

.package-card.enterprise {
    border-color: #ff6b6b;
}

.package-header {
    padding: var(--spacing-2xl);
    text-align: center;
    position: relative;
}

.package-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-purple);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 700;
    text-transform: uppercase;
}

.package-badge.premium {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: var(--primary-black);
}

.package-badge.exclusive {
    background: linear-gradient(135deg, #ff6b6b, #ff4757);
}

.package-header h3 {
    font-size: var(--font-size-2xl);
    font-weight: 800;
    color: var(--primary-black);
    margin: var(--spacing-lg) 0 var(--spacing-sm);
}

.package-subtitle {
    color: var(--light-gray);
    margin-bottom: var(--spacing-lg);
}

.package-price {
    margin-bottom: var(--spacing-lg);
}

.price-old {
    font-size: var(--font-size-lg);
    color: var(--lighter-gray);
    text-decoration: line-through;
    display: block;
}

.price-current {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    color: var(--primary-black);
    display: block;
    margin: var(--spacing-sm) 0;
}

.price-installments {
    font-size: var(--font-size-base);
    color: var(--light-gray);
}

.package-benefits {
    padding: 0 var(--spacing-2xl);
    margin-bottom: var(--spacing-xl);
}

.package-benefits .benefit-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    color: var(--medium-gray);
}

.package-benefits .benefit-icon {
    width: 20px;
    height: 20px;
    background: var(--neon-green);
    color: var(--primary-black);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 800;
    font-size: var(--font-size-xs);
    flex-shrink: 0;
    margin-top: 2px;
}

.package-results {
    background: linear-gradient(135deg, rgba(128, 0, 255, 0.05), rgba(0, 0, 0, 0.02));
    padding: var(--spacing-lg);
    margin: 0 var(--spacing-lg) var(--spacing-xl);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(128, 0, 255, 0.1);
}

.result-title {
    font-weight: 700;
    color: var(--primary-black);
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.result-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.result-list span {
    color: var(--light-gray);
    font-size: var(--font-size-sm);
}

.package-cta {
    width: calc(100% - var(--spacing-4xl));
    margin: 0 var(--spacing-2xl) var(--spacing-2xl);
    background: var(--gradient-primary);
    color: var(--white);
    border: none;
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-weight: 700;
    cursor: pointer;
    transition: var(--transition-normal);
    text-transform: uppercase;
}

.package-cta:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-purple);
}

.package-cta.premium {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: var(--primary-black);
}

.package-cta.exclusive {
    background: linear-gradient(135deg, #ff6b6b, #ff4757);
}

/* Garantias */
.guarantees-section {
    margin-top: var(--spacing-4xl);
    padding: var(--spacing-2xl);
    background: var(--off-white);
    border-radius: var(--radius-2xl);
    text-align: center;
}

.guarantees-section h3 {
    font-size: var(--font-size-2xl);
    font-weight: 800;
    color: var(--primary-black);
    margin-bottom: var(--spacing-2xl);
}

.guarantees-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
}

.guarantee-item {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.guarantee-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.guarantee-icon {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-md);
}

.guarantee-item h4 {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-black);
    margin-bottom: var(--spacing-sm);
}

.guarantee-item p {
    color: var(--light-gray);
    line-height: 1.6;
}

.service-category {
    margin-bottom: var(--spacing-4xl);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    background: linear-gradient(135deg, rgba(128, 0, 255, 0.02) 0%, rgba(0, 0, 0, 0.02) 100%);
    border: 1px solid rgba(128, 0, 255, 0.1);
    transition: var(--transition-normal);
}

.service-category:hover {
    border-color: var(--primary-purple);
    box-shadow: var(--shadow-lg);
}

.service-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.service-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    flex-shrink: 0;
}

.service-icon.marketing {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.service-icon.design {
    background: var(--gradient-primary);
}

.service-icon.web {
    background: linear-gradient(135deg, #00d2d3, #54a0ff);
}

.service-icon svg {
    width: 40px;
    height: 40px;
}

.service-title h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-black);
    margin-bottom: var(--spacing-xs);
}

.service-title p {
    color: var(--light-gray);
    font-size: var(--font-size-base);
}

.service-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.service-item {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(128, 0, 255, 0.1);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.service-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.service-item:hover::before {
    transform: scaleX(1);
}

.service-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-purple);
}

.service-item.featured {
    border-color: var(--primary-purple);
    background: linear-gradient(135deg, rgba(128, 0, 255, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
}

.service-item.featured::before {
    transform: scaleX(1);
}

.service-item h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-black);
    margin-bottom: var(--spacing-md);
}

.service-item p {
    color: var(--light-gray);
    line-height: 1.6;
}

@media (max-width: 768px) {
    .section-title {
        font-size: var(--font-size-3xl);
    }

    .service-header {
        flex-direction: column;
        text-align: center;
    }

    .service-items {
        grid-template-columns: 1fr;
    }

    .sobre-grid {
        grid-template-columns: 1fr;
    }
}

/* ===== DEPOIMENTOS SECTION ===== */
.depoimentos {
    padding: var(--spacing-4xl) 0;
    background: var(--gradient-dark);
    color: var(--white);
}

.depoimentos .section-title {
    color: var(--white);
}

.depoimentos .section-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

.depoimentos-carousel {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    overflow: hidden;
}

.depoimento-card {
    display: none;
    opacity: 0;
    transform: translateX(50px);
    transition: var(--transition-slow);
}

.depoimento-card.active {
    display: block;
    opacity: 1;
    transform: translateX(0);
}

.depoimento-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    text-align: center;
}

.stars {
    font-size: var(--font-size-xl);
    color: #ffd700;
    margin-bottom: var(--spacing-lg);
}

.depoimento-content p {
    font-size: var(--font-size-lg);
    line-height: 1.6;
    margin-bottom: var(--spacing-xl);
    font-style: italic;
}

.depoimento-author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
}

.author-info h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.author-info span {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.7);
}

.carousel-dots {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-2xl);
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: var(--transition-fast);
}

.dot.active {
    background: var(--primary-purple);
    transform: scale(1.2);
}

/* ===== CONTATO SECTION ===== */
.contato {
    padding: var(--spacing-4xl) 0;
    background: var(--off-white);
    position: relative;
}

.contato-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4xl);
    align-items: start;
}

.contato-info h2 {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    color: var(--primary-black);
    margin-bottom: var(--spacing-lg);
}

.contato-info p {
    font-size: var(--font-size-lg);
    color: var(--light-gray);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.6;
}

.contact-benefits {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.benefit {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.benefit-icon {
    width: 24px;
    height: 24px;
    background: var(--gradient-primary);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    font-weight: 600;
    flex-shrink: 0;
}

.benefit span:last-child {
    color: var(--medium-gray);
    font-weight: 500;
}

.contact-form {
    background: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(128, 0, 255, 0.1);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid rgba(128, 0, 255, 0.1);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    transition: var(--transition-fast);
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-purple);
    box-shadow: 0 0 0 3px rgba(128, 0, 255, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.btn-submit {
    width: 100%;
    background: var(--gradient-primary);
    color: var(--white);
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-purple);
}

.form-feedback {
    position: fixed;
    top: 100px;
    right: var(--spacing-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-weight: 500;
    z-index: 1000;
    transform: translateX(400px);
    transition: var(--transition-normal);
}

.form-feedback.show {
    transform: translateX(0);
}

.form-feedback.success {
    background: #10b981;
    color: var(--white);
}

.form-feedback.error {
    background: #ef4444;
    color: var(--white);
}

@media (max-width: 768px) {
    .contato-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }

    .contato-info h2 {
        font-size: var(--font-size-3xl);
    }

    .form-feedback {
        right: var(--spacing-md);
        left: var(--spacing-md);
        transform: translateY(-100px);
    }

    .form-feedback.show {
        transform: translateY(0);
    }
}

/* ===== WHATSAPP BUTTON ===== */
.whatsapp-btn {
    position: fixed;
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
    width: 60px;
    height: 60px;
    background: #25d366;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    transition: var(--transition-normal);
    animation: pulse 2s infinite;
}

.whatsapp-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 10px 30px rgba(37, 211, 102, 0.4);
}

.whatsapp-btn svg {
    width: 30px;
    height: 30px;
}

@media (max-width: 768px) {
    .whatsapp-btn {
        bottom: var(--spacing-lg);
        right: var(--spacing-lg);
        width: 50px;
        height: 50px;
    }

    .whatsapp-btn svg {
        width: 25px;
        height: 25px;
    }
}

/* ===== FOOTER ===== */
.footer {
    background: var(--primary-black);
    color: var(--white);
    padding: var(--spacing-4xl) 0 var(--spacing-xl);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.footer-brand h3 {
    font-size: var(--font-size-2xl);
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-md);
}

.footer-brand p {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6;
}

.footer-column h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    color: var(--white);
}

.footer-column ul {
    list-style: none;
}

.footer-column ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-column ul li a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-column ul li a:hover {
    color: var(--primary-purple);
}

.footer-social h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    color: var(--white);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: var(--transition-fast);
}

.social-links a:hover {
    background: var(--primary-purple);
    color: var(--white);
    transform: translateY(-2px);
}

.social-links a svg {
    width: 20px;
    height: 20px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: var(--spacing-xl);
    text-align: center;
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.5);
    font-size: var(--font-size-sm);
}

@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }

    .social-links {
        justify-content: center;
    }
}

/* ===== ANIMAÇÕES DE SCROLL ===== */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: var(--transition-slow);
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* ===== RESPONSIVIDADE ADICIONAL ===== */
@media (max-width: 480px) {
    :root {
        --font-size-5xl: 2.25rem;
        --font-size-4xl: 1.875rem;
        --font-size-3xl: 1.5rem;
    }

    .hero {
        min-height: 90vh;
    }

    .hero-cta {
        gap: var(--spacing-md);
    }

    .btn-primary,
    .btn-secondary {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--font-size-sm);
    }

    .section-header {
        margin-bottom: var(--spacing-2xl);
    }

    .sobre,
    .servicos,
    .depoimentos,
    .contato {
        padding: var(--spacing-2xl) 0;
    }
}

/* ===== MELHORIAS DE PERFORMANCE ===== */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

img {
    max-width: 100%;
    height: auto;
}

/* ===== ESTADOS DE LOADING ===== */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--primary-purple);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
