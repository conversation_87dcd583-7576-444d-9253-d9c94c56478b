// ===== CONFIGURAÇÕES GLOBAIS =====
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Inicializar todas as funcionalidades
    initNavigation();
    initScrollAnimations();
    initCarousel();
    initContactForm();
    initScrollIndicator();
    initHeaderScroll();
    initTechCanvas();
    initHolographicEffects();
}

// ===== NAVEGAÇÃO MOBILE =====
function initNavigation() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-menu a');

    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
            document.body.style.overflow = navMenu.classList.contains('active') ? 'hidden' : '';
        });

        // Fechar menu ao clicar em um link
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
                document.body.style.overflow = '';
            });
        });

        // Fechar menu ao clicar fora
        document.addEventListener('click', function(e) {
            if (!hamburger.contains(e.target) && !navMenu.contains(e.target)) {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
    }
}

// ===== HEADER SCROLL EFFECT =====
function initHeaderScroll() {
    const header = document.querySelector('.header');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
}

// ===== ANIMAÇÕES DE SCROLL =====
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('revealed');
            }
        });
    }, observerOptions);

    // Observar elementos que devem ser animados
    const elementsToAnimate = document.querySelectorAll('.sobre-card, .service-category, .depoimento-card');
    elementsToAnimate.forEach(el => {
        el.classList.add('scroll-reveal');
        observer.observe(el);
    });
}

// ===== CARROSSEL DE DEPOIMENTOS =====
function initCarousel() {
    let currentSlide = 0;
    const slides = document.querySelectorAll('.depoimento-card');
    const dots = document.querySelectorAll('.dot');
    
    if (slides.length === 0) return;

    function showSlide(index) {
        // Remover classe active de todos os slides e dots
        slides.forEach(slide => slide.classList.remove('active'));
        dots.forEach(dot => dot.classList.remove('active'));
        
        // Adicionar classe active ao slide e dot atual
        slides[index].classList.add('active');
        dots[index].classList.add('active');
        
        currentSlide = index;
    }

    // Auto-play do carrossel
    function nextSlide() {
        currentSlide = (currentSlide + 1) % slides.length;
        showSlide(currentSlide);
    }

    // Iniciar auto-play
    setInterval(nextSlide, 5000);

    // Mostrar primeiro slide
    showSlide(0);
}

// Função para navegação manual do carrossel
function currentSlide(index) {
    const slides = document.querySelectorAll('.depoimento-card');
    const dots = document.querySelectorAll('.dot');
    
    slides.forEach(slide => slide.classList.remove('active'));
    dots.forEach(dot => dot.classList.remove('active'));
    
    slides[index - 1].classList.add('active');
    dots[index - 1].classList.add('active');
}

// ===== FORMULÁRIO DE CONTATO =====
function initContactForm() {
    const form = document.getElementById('contactForm');
    const feedback = document.getElementById('formFeedback');

    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Adicionar estado de loading
            const submitBtn = form.querySelector('.btn-submit');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Enviando...';
            submitBtn.disabled = true;
            submitBtn.classList.add('loading');

            // Simular envio (aqui você integraria com seu backend)
            setTimeout(() => {
                // Resetar botão
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
                submitBtn.classList.remove('loading');

                // Mostrar feedback de sucesso
                showFormFeedback('Mensagem enviada com sucesso! Entraremos em contato em breve.', 'success');
                
                // Limpar formulário
                form.reset();
            }, 2000);
        });
    }
}

function showFormFeedback(message, type) {
    const feedback = document.getElementById('formFeedback');
    feedback.textContent = message;
    feedback.className = `form-feedback ${type}`;
    feedback.classList.add('show');

    // Remover feedback após 5 segundos
    setTimeout(() => {
        feedback.classList.remove('show');
    }, 5000);
}

// ===== SCROLL SUAVE PARA SEÇÕES =====
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        const headerHeight = document.querySelector('.header').offsetHeight;
        const sectionTop = section.offsetTop - headerHeight;
        
        window.scrollTo({
            top: sectionTop,
            behavior: 'smooth'
        });
    }
}

function scrollToContact() {
    scrollToSection('contato');
}

function scrollToServices() {
    scrollToSection('servicos');
}

// ===== INDICADOR DE SCROLL =====
function initScrollIndicator() {
    const scrollIndicator = document.querySelector('.scroll-indicator');
    
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', function() {
            scrollToSection('sobre');
        });

        // Esconder indicador após scroll
        window.addEventListener('scroll', function() {
            if (window.scrollY > 200) {
                scrollIndicator.style.opacity = '0';
            } else {
                scrollIndicator.style.opacity = '1';
            }
        });
    }
}

// ===== EFEITOS DE HOVER AVANÇADOS =====
document.addEventListener('DOMContentLoaded', function() {
    // Efeito parallax sutil nos elementos flutuantes
    const floatingElements = document.querySelectorAll('.floating-element');
    
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;
        
        floatingElements.forEach((element, index) => {
            const speed = (index + 1) * 0.3;
            element.style.transform = `translateY(${rate * speed}px)`;
        });
    });

    // Efeito de mouse nos cards
    const cards = document.querySelectorAll('.sobre-card, .service-item, .hero-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});

// ===== OTIMIZAÇÕES DE PERFORMANCE =====
// Debounce para eventos de scroll
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Aplicar debounce aos eventos de scroll
const debouncedScrollHandler = debounce(function() {
    // Handlers de scroll otimizados aqui
}, 10);

window.addEventListener('scroll', debouncedScrollHandler);

// ===== ACESSIBILIDADE =====
// Navegação por teclado
document.addEventListener('keydown', function(e) {
    // ESC para fechar menu mobile
    if (e.key === 'Escape') {
        const hamburger = document.querySelector('.hamburger');
        const navMenu = document.querySelector('.nav-menu');
        
        if (hamburger && navMenu && navMenu.classList.contains('active')) {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
            document.body.style.overflow = '';
        }
    }
});

// ===== ANALYTICS E TRACKING =====
function trackEvent(eventName, eventData = {}) {
    // Aqui você pode integrar com Google Analytics, Facebook Pixel, etc.
    console.log('Event tracked:', eventName, eventData);
    
    // Exemplo de integração com Google Analytics
    if (typeof gtag !== 'undefined') {
        gtag('event', eventName, eventData);
    }
}

// Rastrear cliques nos CTAs
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('btn-primary')) {
        trackEvent('cta_click', {
            button_text: e.target.textContent,
            page_section: e.target.closest('section')?.id || 'unknown'
        });
    }
    
    if (e.target.closest('.whatsapp-btn')) {
        trackEvent('whatsapp_click', {
            page_url: window.location.href
        });
    }
});

// ===== LAZY LOADING PARA IMAGENS =====
function initLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// Inicializar lazy loading
initLazyLoading();

// ===== SISTEMA DE PARTÍCULAS TECNOLÓGICAS =====
function initTechCanvas() {
    const canvas = document.getElementById('techCanvas');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    const particles = [];
    const connections = [];
    const particleCount = 80;

    class TechParticle {
        constructor() {
            this.x = Math.random() * canvas.width;
            this.y = Math.random() * canvas.height;
            this.vx = (Math.random() - 0.5) * 0.8;
            this.vy = (Math.random() - 0.5) * 0.8;
            this.size = Math.random() * 3 + 1;
            this.opacity = Math.random() * 0.8 + 0.2;
            this.hue = Math.random() * 60 + 240; // Roxo/azul
            this.pulse = Math.random() * Math.PI * 2;
        }

        update() {
            this.x += this.vx;
            this.y += this.vy;
            this.pulse += 0.02;

            // Bounce off edges
            if (this.x < 0 || this.x > canvas.width) this.vx *= -1;
            if (this.y < 0 || this.y > canvas.height) this.vy *= -1;

            // Keep in bounds
            this.x = Math.max(0, Math.min(canvas.width, this.x));
            this.y = Math.max(0, Math.min(canvas.height, this.y));
        }

        draw() {
            const pulseSize = this.size + Math.sin(this.pulse) * 0.5;
            const pulseOpacity = this.opacity + Math.sin(this.pulse) * 0.2;

            // Outer glow
            ctx.beginPath();
            ctx.arc(this.x, this.y, pulseSize * 3, 0, Math.PI * 2);
            ctx.fillStyle = `hsla(${this.hue}, 100%, 50%, ${pulseOpacity * 0.1})`;
            ctx.fill();

            // Core particle
            ctx.beginPath();
            ctx.arc(this.x, this.y, pulseSize, 0, Math.PI * 2);
            ctx.fillStyle = `hsla(${this.hue}, 100%, 70%, ${pulseOpacity})`;
            ctx.fill();

            // Inner core
            ctx.beginPath();
            ctx.arc(this.x, this.y, pulseSize * 0.3, 0, Math.PI * 2);
            ctx.fillStyle = `hsla(${this.hue}, 100%, 90%, ${pulseOpacity})`;
            ctx.fill();
        }
    }

    // Create particles
    for (let i = 0; i < particleCount; i++) {
        particles.push(new TechParticle());
    }

    function drawConnections() {
        for (let i = 0; i < particles.length; i++) {
            for (let j = i + 1; j < particles.length; j++) {
                const dx = particles[i].x - particles[j].x;
                const dy = particles[i].y - particles[j].y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < 120) {
                    const opacity = (1 - distance / 120) * 0.5;
                    const gradient = ctx.createLinearGradient(
                        particles[i].x, particles[i].y,
                        particles[j].x, particles[j].y
                    );
                    gradient.addColorStop(0, `hsla(${particles[i].hue}, 100%, 50%, ${opacity})`);
                    gradient.addColorStop(1, `hsla(${particles[j].hue}, 100%, 50%, ${opacity})`);

                    ctx.beginPath();
                    ctx.moveTo(particles[i].x, particles[i].y);
                    ctx.lineTo(particles[j].x, particles[j].y);
                    ctx.strokeStyle = gradient;
                    ctx.lineWidth = 1;
                    ctx.stroke();
                }
            }
        }
    }

    function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Draw connections first
        drawConnections();

        // Update and draw particles
        particles.forEach(particle => {
            particle.update();
            particle.draw();
        });

        requestAnimationFrame(animate);
    }

    animate();

    // Handle resize
    window.addEventListener('resize', () => {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    });

    // Mouse interaction
    canvas.addEventListener('mousemove', (e) => {
        const rect = canvas.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        particles.forEach(particle => {
            const dx = mouseX - particle.x;
            const dy = mouseY - particle.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < 100) {
                const force = (100 - distance) / 100;
                particle.vx += (dx / distance) * force * 0.01;
                particle.vy += (dy / distance) * force * 0.01;
            }
        });
    });
}

// ===== EFEITOS HOLOGRÁFICOS =====
function initHolographicEffects() {
    // Efeito de scan nas métricas
    const metrics = document.querySelectorAll('.metric-number');
    metrics.forEach((metric, index) => {
        metric.setAttribute('data-number', metric.textContent);

        // Adicionar efeito de scan
        setInterval(() => {
            metric.style.textShadow = `
                0 0 5px rgba(128, 0, 255, 0.8),
                0 0 10px rgba(128, 0, 255, 0.6),
                0 0 15px rgba(128, 0, 255, 0.4),
                0 0 20px rgba(128, 0, 255, 0.2)
            `;

            setTimeout(() => {
                metric.style.textShadow = '0 0 20px rgba(128, 0, 255, 0.5)';
            }, 200);
        }, 3000 + index * 500);
    });

    // Efeito de glitch no título
    const title = document.querySelector('.hero-title');
    if (title) {
        setInterval(() => {
            title.style.transform = 'translateX(2px)';
            title.style.filter = 'hue-rotate(10deg)';

            setTimeout(() => {
                title.style.transform = 'translateX(-1px)';
                setTimeout(() => {
                    title.style.transform = 'translateX(0)';
                    title.style.filter = 'hue-rotate(0deg)';
                }, 50);
            }, 50);
        }, 8000);
    }

    // Efeito de energia nos botões
    const buttons = document.querySelectorAll('.btn-primary, .btn-secondary');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', () => {
            button.style.animation = 'energyPulse 0.6s ease-out';
        });

        button.addEventListener('animationend', () => {
            button.style.animation = '';
        });
    });
}

// Adicionar CSS para animações
const techCSS = `
@keyframes energyPulse {
    0% { box-shadow: 0 0 20px rgba(128, 0, 255, 0.5); }
    50% { box-shadow: 0 0 40px rgba(128, 0, 255, 1), 0 0 60px rgba(0, 212, 255, 0.8); }
    100% { box-shadow: 0 0 20px rgba(128, 0, 255, 0.5); }
}

@keyframes dataFlow {
    0% { transform: translateX(-100%); opacity: 0; }
    50% { opacity: 1; }
    100% { transform: translateX(100%); opacity: 0; }
}
`;

const techStyle = document.createElement('style');
techStyle.textContent = techCSS;
document.head.appendChild(techStyle);
