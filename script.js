// ===== CONFIGURAÇÕES GLOBAIS =====
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Inicializar todas as funcionalidades
    initNavigation();
    initScrollAnimations();
    initCarousel();
    initContactForm();
    initScrollIndicator();
    initHeaderScroll();
    initUrgencyTimer();
    initUrgencyCounter();
    initParticleSystem();
    initNeuralTriggers();
}

// ===== NAVEGAÇÃO MOBILE =====
function initNavigation() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-menu a');

    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
            document.body.style.overflow = navMenu.classList.contains('active') ? 'hidden' : '';
        });

        // Fechar menu ao clicar em um link
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
                document.body.style.overflow = '';
            });
        });

        // Fechar menu ao clicar fora
        document.addEventListener('click', function(e) {
            if (!hamburger.contains(e.target) && !navMenu.contains(e.target)) {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
    }
}

// ===== HEADER SCROLL EFFECT =====
function initHeaderScroll() {
    const header = document.querySelector('.header');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
}

// ===== ANIMAÇÕES DE SCROLL =====
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('revealed');
            }
        });
    }, observerOptions);

    // Observar elementos que devem ser animados
    const elementsToAnimate = document.querySelectorAll('.sobre-card, .service-category, .depoimento-card');
    elementsToAnimate.forEach(el => {
        el.classList.add('scroll-reveal');
        observer.observe(el);
    });
}

// ===== CARROSSEL DE DEPOIMENTOS =====
function initCarousel() {
    let currentSlide = 0;
    const slides = document.querySelectorAll('.depoimento-card');
    const dots = document.querySelectorAll('.dot');
    
    if (slides.length === 0) return;

    function showSlide(index) {
        // Remover classe active de todos os slides e dots
        slides.forEach(slide => slide.classList.remove('active'));
        dots.forEach(dot => dot.classList.remove('active'));
        
        // Adicionar classe active ao slide e dot atual
        slides[index].classList.add('active');
        dots[index].classList.add('active');
        
        currentSlide = index;
    }

    // Auto-play do carrossel
    function nextSlide() {
        currentSlide = (currentSlide + 1) % slides.length;
        showSlide(currentSlide);
    }

    // Iniciar auto-play
    setInterval(nextSlide, 5000);

    // Mostrar primeiro slide
    showSlide(0);
}

// Função para navegação manual do carrossel
function currentSlide(index) {
    const slides = document.querySelectorAll('.depoimento-card');
    const dots = document.querySelectorAll('.dot');
    
    slides.forEach(slide => slide.classList.remove('active'));
    dots.forEach(dot => dot.classList.remove('active'));
    
    slides[index - 1].classList.add('active');
    dots[index - 1].classList.add('active');
}

// ===== FORMULÁRIO DE CONTATO =====
function initContactForm() {
    const form = document.getElementById('contactForm');
    const feedback = document.getElementById('formFeedback');

    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Adicionar estado de loading
            const submitBtn = form.querySelector('.btn-submit');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Enviando...';
            submitBtn.disabled = true;
            submitBtn.classList.add('loading');

            // Simular envio (aqui você integraria com seu backend)
            setTimeout(() => {
                // Resetar botão
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
                submitBtn.classList.remove('loading');

                // Mostrar feedback de sucesso
                showFormFeedback('Mensagem enviada com sucesso! Entraremos em contato em breve.', 'success');
                
                // Limpar formulário
                form.reset();
            }, 2000);
        });
    }
}

function showFormFeedback(message, type) {
    const feedback = document.getElementById('formFeedback');
    feedback.textContent = message;
    feedback.className = `form-feedback ${type}`;
    feedback.classList.add('show');

    // Remover feedback após 5 segundos
    setTimeout(() => {
        feedback.classList.remove('show');
    }, 5000);
}

// ===== SCROLL SUAVE PARA SEÇÕES =====
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        const headerHeight = document.querySelector('.header').offsetHeight;
        const sectionTop = section.offsetTop - headerHeight;
        
        window.scrollTo({
            top: sectionTop,
            behavior: 'smooth'
        });
    }
}

function scrollToContact() {
    scrollToSection('contato');
}

function scrollToServices() {
    scrollToSection('servicos');
}

// ===== INDICADOR DE SCROLL =====
function initScrollIndicator() {
    const scrollIndicator = document.querySelector('.scroll-indicator');
    
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', function() {
            scrollToSection('sobre');
        });

        // Esconder indicador após scroll
        window.addEventListener('scroll', function() {
            if (window.scrollY > 200) {
                scrollIndicator.style.opacity = '0';
            } else {
                scrollIndicator.style.opacity = '1';
            }
        });
    }
}

// ===== EFEITOS DE HOVER AVANÇADOS =====
document.addEventListener('DOMContentLoaded', function() {
    // Efeito parallax sutil nos elementos flutuantes
    const floatingElements = document.querySelectorAll('.floating-element');
    
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;
        
        floatingElements.forEach((element, index) => {
            const speed = (index + 1) * 0.3;
            element.style.transform = `translateY(${rate * speed}px)`;
        });
    });

    // Efeito de mouse nos cards
    const cards = document.querySelectorAll('.sobre-card, .service-item, .hero-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});

// ===== OTIMIZAÇÕES DE PERFORMANCE =====
// Debounce para eventos de scroll
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Aplicar debounce aos eventos de scroll
const debouncedScrollHandler = debounce(function() {
    // Handlers de scroll otimizados aqui
}, 10);

window.addEventListener('scroll', debouncedScrollHandler);

// ===== ACESSIBILIDADE =====
// Navegação por teclado
document.addEventListener('keydown', function(e) {
    // ESC para fechar menu mobile
    if (e.key === 'Escape') {
        const hamburger = document.querySelector('.hamburger');
        const navMenu = document.querySelector('.nav-menu');
        
        if (hamburger && navMenu && navMenu.classList.contains('active')) {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
            document.body.style.overflow = '';
        }
    }
});

// ===== ANALYTICS E TRACKING =====
function trackEvent(eventName, eventData = {}) {
    // Aqui você pode integrar com Google Analytics, Facebook Pixel, etc.
    console.log('Event tracked:', eventName, eventData);
    
    // Exemplo de integração com Google Analytics
    if (typeof gtag !== 'undefined') {
        gtag('event', eventName, eventData);
    }
}

// Rastrear cliques nos CTAs
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('btn-primary')) {
        trackEvent('cta_click', {
            button_text: e.target.textContent,
            page_section: e.target.closest('section')?.id || 'unknown'
        });
    }
    
    if (e.target.closest('.whatsapp-btn')) {
        trackEvent('whatsapp_click', {
            page_url: window.location.href
        });
    }
});

// ===== LAZY LOADING PARA IMAGENS =====
function initLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// Inicializar lazy loading
initLazyLoading();

// ===== FUNCIONALIDADES DE NEUROCIÊNCIA E PERSUASÃO =====

// Timer de urgência
function initUrgencyTimer() {
    const timerElement = document.getElementById('urgencyTimer');
    if (!timerElement) return;

    // Definir tempo inicial (23:45:12)
    let hours = 23;
    let minutes = 45;
    let seconds = 12;

    function updateTimer() {
        seconds--;

        if (seconds < 0) {
            seconds = 59;
            minutes--;

            if (minutes < 0) {
                minutes = 59;
                hours--;

                if (hours < 0) {
                    // Reiniciar timer
                    hours = 23;
                    minutes = 45;
                    seconds = 12;
                }
            }
        }

        const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        timerElement.textContent = formattedTime;
    }

    // Atualizar a cada segundo
    setInterval(updateTimer, 1000);
}

// Contador de urgência (vagas restantes)
function initUrgencyCounter() {
    const counterElement = document.getElementById('urgencyCounter');
    if (!counterElement) return;

    let count = 7;

    // Diminuir contador a cada 30 segundos
    setInterval(() => {
        if (count > 2) {
            count--;
            counterElement.textContent = count;

            // Efeito visual de urgência
            counterElement.style.color = '#ff4757';
            counterElement.style.fontWeight = '800';

            setTimeout(() => {
                counterElement.style.color = '';
                counterElement.style.fontWeight = '';
            }, 2000);
        }
    }, 30000);
}

// Sistema de partículas neurais
function initParticleSystem() {
    const canvas = document.getElementById('particleCanvas');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    const particles = [];
    const particleCount = 50;

    class Particle {
        constructor() {
            this.x = Math.random() * canvas.width;
            this.y = Math.random() * canvas.height;
            this.vx = (Math.random() - 0.5) * 0.5;
            this.vy = (Math.random() - 0.5) * 0.5;
            this.size = Math.random() * 2 + 1;
            this.opacity = Math.random() * 0.5 + 0.2;
        }

        update() {
            this.x += this.vx;
            this.y += this.vy;

            if (this.x < 0 || this.x > canvas.width) this.vx *= -1;
            if (this.y < 0 || this.y > canvas.height) this.vy *= -1;
        }

        draw() {
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(128, 0, 255, ${this.opacity})`;
            ctx.fill();
        }
    }

    // Criar partículas
    for (let i = 0; i < particleCount; i++) {
        particles.push(new Particle());
    }

    function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        particles.forEach(particle => {
            particle.update();
            particle.draw();
        });

        // Conectar partículas próximas
        for (let i = 0; i < particles.length; i++) {
            for (let j = i + 1; j < particles.length; j++) {
                const dx = particles[i].x - particles[j].x;
                const dy = particles[i].y - particles[j].y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < 100) {
                    ctx.beginPath();
                    ctx.moveTo(particles[i].x, particles[i].y);
                    ctx.lineTo(particles[j].x, particles[j].y);
                    ctx.strokeStyle = `rgba(128, 0, 255, ${0.1 * (1 - distance / 100)})`;
                    ctx.stroke();
                }
            }
        }

        requestAnimationFrame(animate);
    }

    animate();

    // Redimensionar canvas
    window.addEventListener('resize', () => {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    });
}

// Gatilhos neurais avançados
function initNeuralTriggers() {
    // Efeito de escassez visual
    const urgencyBar = document.querySelector('.urgency-bar');
    if (urgencyBar) {
        setInterval(() => {
            urgencyBar.style.background = 'linear-gradient(90deg, #ff6b6b, #ff4757)';
            setTimeout(() => {
                urgencyBar.style.background = 'linear-gradient(90deg, #ff4757, #ff3742)';
            }, 500);
        }, 3000);
    }

    // Animação de números crescentes
    const resultMetrics = document.querySelectorAll('.result-metric, .stat-number');

    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px'
    };

    const numberObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateNumber(entry.target);
                numberObserver.unobserve(entry.target);
            }
        });
    }, observerOptions);

    resultMetrics.forEach(metric => {
        numberObserver.observe(metric);
    });

    // Efeito de mouse nos CTAs
    const ctaButtons = document.querySelectorAll('.btn-primary, .package-cta');

    ctaButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.05)';
            this.style.boxShadow = '0 15px 40px rgba(255, 107, 107, 0.6)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 10px 30px rgba(255, 107, 107, 0.4)';
        });
    });

    // Efeito de clique nos CTAs
    ctaButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Efeito de ripple
            const ripple = document.createElement('span');
            ripple.style.position = 'absolute';
            ripple.style.borderRadius = '50%';
            ripple.style.background = 'rgba(255, 255, 255, 0.6)';
            ripple.style.transform = 'scale(0)';
            ripple.style.animation = 'ripple 0.6s linear';
            ripple.style.left = '50%';
            ripple.style.top = '50%';
            ripple.style.width = '20px';
            ripple.style.height = '20px';
            ripple.style.marginLeft = '-10px';
            ripple.style.marginTop = '-10px';

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

// Animar números crescentes
function animateNumber(element) {
    const text = element.textContent;
    const number = parseInt(text.replace(/[^\d]/g, ''));
    const prefix = text.replace(/[\d]/g, '').replace(/\+/g, '');
    const hasPlus = text.includes('+');

    if (isNaN(number)) return;

    let current = 0;
    const increment = number / 50;
    const timer = setInterval(() => {
        current += increment;
        if (current >= number) {
            current = number;
            clearInterval(timer);
        }

        const displayNumber = Math.floor(current);
        element.textContent = prefix + displayNumber + (hasPlus ? '+' : '');
    }, 30);
}

// Adicionar CSS para animação de ripple
const rippleCSS = `
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}
`;

const style = document.createElement('style');
style.textContent = rippleCSS;
document.head.appendChild(style);
