Crie uma landing page moderna, impactante e com alto apelo visual e emocional, utilizando exclusivamente HTML, CSS e JavaScript (sem frameworks). A landing page é para a empresa chamada **Flecty**, que atua nas áreas de **marketing digital, social media, design gráfico e desenvolvimento de sites**.

### ⚙️ Requisitos técnicos:
- Apenas HTML5, CSS3 e JavaScript puro (sem bibliotecas ou frameworks).
- Código bem organizado, responsivo e com comentários explicativos.
- Design mobile-first, com adaptação fluida a diferentes resoluções.

### 🎨 Paleta de cores:
- Cor principal: **roxo (purple)** (#8000ff ou similar).
- Cor secundária: **preto** (#000000).
- Use tons combinantes suaves (ex: cinza escuro, lilás, branco ou degradês em neon discreto) para contraste e equilíbrio visual, sem poluir o design.

### ✨ Estilo visual e sensorial:
- Estética altamente **moderna**, **elegante**, **minimalista**, com **elementos de UI clean** e tipografia marcante.
- Utilize princípios de **neurociência aplicada ao marketing** para estimular conversão:
  - Gatilhos visuais de confiança (ex: depoimentos, selos de credibilidade).
  - CTA ("Chamada para Ação") com cores contrastantes e posicionamento estratégico.
  - Princípios de escassez, autoridade e reciprocidade.
  - Microanimações e transições suaves com JavaScript/CSS para aumentar engajamento sem distrações.
  - Estrutura em F ou Z para guiar o olhar do visitante.
  - Hierarquia visual forte (títulos grandes, blocos com espaçamento inteligente).

### 📄 Estrutura da Landing Page:
1. **Hero Section (Topo):**
   - Fundo com visual impressionante (ex: gradiente, imagem estilizada ou elemento animado).
   - Título impactante que mostre a proposta da Flecty.
   - Subtítulo com foco em benefícios.
   - Botão CTA chamativo (“Solicite um Orçamento” ou “Fale com a Flecty”).

2. **Sobre a Flecty:**
   - Bloco com breve explicação sobre a missão e diferenciais da empresa.
   - Ícones ou imagens vetoriais para ilustrar os serviços.

3. **Serviços oferecidos (em destaque por categoria):**

   #### 📱 Marketing Digital & Social Media:
   - Gestão de redes sociais.
   - Criação de conteúdo estratégico.
   - Planejamento e análise de métricas.
   - Destaque com ícones modernos e textos objetivos.

   #### 🎨 Design Gráfico (ênfase nos seguintes):
   - **Criação de logotipo profissional** (com foco na identidade da marca).
   - **Identidade visual completa** (paleta, tipografia, manual de marca).
   - **Convites personalizados** para eventos como:
     - Aniversário
     - Casamento
     - Eventos corporativos
   - Elementos gráficos dinâmicos, animações suaves e exibição com mockups.

   #### 💻 Desenvolvimento Web:
   - Desenvolvimento exclusivo de **sites personalizados**, como:
     - **Landing pages otimizadas para conversão**.
     - **Sites de convite digital** (casamento, aniversário, eventos).
   - Interface visual responsiva, leve, funcional e elegante.
   - Blocos com animações on-scroll e chamadas para ação.

4. **Depoimentos ou Prova Social:**
   - Carrossel simples ou seção estática com comentários de clientes satisfeitos.
   - Pode conter fotos reais ou ilustrações estilizadas.

5. **Contato (Formulário):**
   - Formulário simples e direto com nome, e-mail, telefone e mensagem.
   - Feedback visual com JS ao enviar (mensagem de sucesso ou erro).
   - Botão de WhatsApp fixo/flutuante com ícone, cor contrastante (ex: verde-limão para destacar).

6. **Rodapé:**
   - Links rápidos (sobre, serviços, contato).
   - Redes sociais com ícones clicáveis.
   - Copyright com estilo discreto.

### 💡 Inspirações e estética:
- Use referências de design de agências criativas e empresas tech.
- Interface que transmita profissionalismo, inovação e confiança.
- Pode usar animações de entrada suaves (scroll reveal, hover, parallax básico, etc).

No final, o site deve **estimular o visitante a entrar em contato com a Flecty**, com uma proposta clara de valor, gatilhos emocionais bem posicionados e um layout neuroestético que favoreça **retenção, escaneabilidade e conversão**.

#################

Observação:
Fale comigo somente em ptbr e faça sempre o git local quando fizer alterações no código, ok?
O commit deve ser realizado somente após cada alteração no código, nunca antes, ok?